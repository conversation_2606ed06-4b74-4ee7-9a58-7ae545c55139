/* Importar tema oficial SUSTENTA */
@import "./styles/sustenta-theme.css";
@import "./styles/accessibility-improvements.css";

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Variables CSS para temas */
:root {
  --theme-transition-duration: 300ms;
  --theme-transition-timing: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Transiciones de tema */
.theme-transitions,
.theme-transitions *,
.theme-transitions *:before,
.theme-transitions *:after {
  transition: background-color var(--theme-transition-duration)
      var(--theme-transition-timing),
    border-color var(--theme-transition-duration) var(--theme-transition-timing),
    color var(--theme-transition-duration) var(--theme-transition-timing),
    fill var(--theme-transition-duration) var(--theme-transition-timing),
    stroke var(--theme-transition-duration) var(--theme-transition-timing),
    box-shadow var(--theme-transition-duration) var(--theme-transition-timing) !important;
}

/* Modo oscuro - colores base */
.dark {
  color-scheme: dark;
}

/* Alto contraste */
.high-contrast {
  --tw-contrast: contrast(1.5);
  filter: var(--tw-contrast);
}

/* Clases utilitarias para modo oscuro */
@layer utilities {
  .bg-theme-primary {
    @apply bg-white dark:bg-dark-primary;
  }

  .bg-theme-secondary {
    @apply bg-gray-50 dark:bg-dark-secondary;
  }

  .bg-theme-tertiary {
    @apply bg-gray-100 dark:bg-dark-tertiary;
  }

  .text-theme-primary {
    @apply text-gray-900 dark:text-dark-primary;
  }

  .text-theme-secondary {
    @apply text-gray-600 dark:text-dark-secondary;
  }

  .text-theme-muted {
    @apply text-gray-500 dark:text-dark-muted;
  }

  .border-theme {
    @apply border-gray-200 dark:border-dark-border;
  }

  .border-theme-light {
    @apply border-gray-300 dark:border-dark-border-light;
  }

  .shadow-theme {
    @apply shadow-sm dark:shadow-lg dark:shadow-black/20;
  }

  .ring-theme {
    @apply ring-gray-200 dark:ring-dark-border;
  }
}

/* === APLICACIÓN DE FUENTES === */
body {
  font-family: var(--font-family-sans);
  background-color: var(--color-background-primary);
  color: var(--color-text-primary);
}

/* === TÍTULOS DE SECCIÓN ESTANDARIZADOS === */
.section-title {
  font-family: var(--font-family-sans);
  font-size: 1.5rem; /* 24px - text-2xl */
  font-weight: 700; /* font-bold */
  color: white;
  line-height: 1.2;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

/* Variante para modo oscuro */
.dark .section-title {
  color: white;
}

/* Subtítulos para modales y componentes internos */
.section-subtitle {
  font-family: var(--font-family-sans);
  font-size: 1.125rem; /* 18px - text-lg */
  font-weight: 600; /* font-semibold */
  color: white;
  line-height: 1.2;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.dark .section-subtitle {
  color: white;
}

/* === CUSTOM FULLCALENDAR OVERRIDES === */
.fc {
  font-family: var(--font-family-sans);
}

.fc-button {
  background-color: var(--color-primary) !important;
  border-color: var(--color-primary) !important;
  color: var(--color-text-inverse) !important;
  font-weight: var(--font-weight-semibold);
  border-radius: var(--border-radius);
  padding: var(--spacing-2) var(--spacing-4);
  transition: var(--transition-fast);
}

.fc-button:hover {
  background-color: var(--color-primary-600) !important;
  border-color: var(--color-primary-600) !important;
  transform: translateY(-1px);
}

.fc-button:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

.fc-button-primary:not(:disabled):active,
.fc-button-primary:not(:disabled).fc-button-active {
  background-color: var(--color-primary-700) !important;
  border-color: var(--color-primary-700) !important;
}

.fc-today-button {
  background-color: var(--color-text-muted) !important;
  border-color: var(--color-text-muted) !important;
}

.fc-today-button:hover {
  background-color: var(--color-text-secondary) !important;
  border-color: var(--color-text-secondary) !important;
}

.fc-daygrid-event {
  border-radius: var(--border-radius);
  border-left: 4px solid;
  font-size: var(--font-size-sm);
  padding: var(--spacing-1);
}

.fc-event-title {
  font-weight: var(--font-weight-medium);
}

.fc-col-header-cell {
  background-color: var(--color-primary) !important;
  color: var(--color-text-inverse) !important;
  font-weight: var(--font-weight-semibold);
  padding: var(--spacing-3) !important;
  border-color: var(--color-primary-600) !important;
}

.fc-daygrid-day-number {
  color: var(--color-text-primary);
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-2);
}

.fc-day-today {
  background-color: var(--color-primary-50) !important;
}

.fc-day-today .fc-daygrid-day-number {
  color: var(--color-primary);
  font-weight: var(--font-weight-bold);
}

/* FullCalendar modo oscuro */
.dark .fc-theme-standard .fc-scrollgrid {
  border-color: var(--color-border) !important;
}

.dark .fc-theme-standard td,
.dark .fc-theme-standard th {
  border-color: var(--color-border) !important;
}

.dark .fc-daygrid-day-frame {
  background-color: var(--color-background-primary) !important;
}

.dark .fc-daygrid-day:hover {
  background-color: var(--color-background-tertiary) !important;
}

.dark .fc-event {
  border-opacity: 0.6;
}

.dark .fc-col-header-cell {
  background-color: var(--color-primary-600) !important;
}

.dark .fc-day-today {
  background-color: var(--color-primary-900) !important;
}
