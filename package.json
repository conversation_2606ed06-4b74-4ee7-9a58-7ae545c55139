{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@fullcalendar/core": "^6.1.19", "@fullcalendar/daygrid": "^6.1.19", "@fullcalendar/interaction": "^6.1.19", "@fullcalendar/react": "^6.1.19", "@fullcalendar/timegrid": "^6.1.19", "@headlessui/react": "^2.2.7", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.2.1", "@tanstack/react-table": "^8.21.3", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dexie": "^4.2.0", "jszip": "^3.10.1", "lucide-react": "^0.344.0", "pdf-lib": "^1.17.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.62.0", "recharts": "^3.1.2", "xlsx": "^0.18.5", "zod": "^4.1.5", "zustand": "^5.0.8"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/jszip": "^3.4.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "terser": "^5.44.0", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}