# 🔐 **CREDENCIALES DEL SISTEMA SUSTENTA**

## 📋 **INFORMACIÓN GENERAL**

Este documento contiene las credenciales de acceso para los diferentes roles del sistema SUSTENTA. Estas credenciales están configuradas para el ambiente de demostración y presentación al cliente.

---

## 👥 **ROLES Y CREDENCIALES DE ACCESO**

### 🔧 **ADMINISTRADOR**

**Rol:** Administrador del Sistema
**Permisos:** Acceso completo a todas las funcionalidades

**Credenciales (Opción 1 - Original):**

- **Usuario:** `11.111.111-1`
- **Contraseña:** `admin`

**Credenciales (Opción 2 - Nueva):**

- **Usuario:** `<EMAIL>`
- **Contraseña:** `admin123`

**Funcionalidades disponibles:**

- ✅ Gestión de Cursos
- ✅ Asistencia y Calificaciones
- ✅ Gestión de Certificados
- ✅ Gestión de Contratistas
- ✅ Reportes Avanzados
- ✅ Configuración del Sistema

---

### 🏢 **CONTRATISTA**

**Rol:** Empresa Contratista
**Permisos:** Gestión de sus participantes y cursos asignados

**Credenciales (Opción 1 - Original):**

- **Usuario:** `22.222.222-2`
- **Contraseña:** `1234`

**Credenciales (Opción 2 - Nueva):**

- **Usuario:** `<EMAIL>`
- **Contraseña:** `contratista123`

**Funcionalidades disponibles:**

- ✅ Dashboard de Contratista
- ✅ Calendario de Cursos
- ✅ Inscripción de Participantes
- ✅ Reportes de sus Participantes (sin métricas globales)
- ✅ Gestión de Asistencia

---

### 👤 **PARTICIPANTE**

**Rol:** Trabajador/Estudiante
**Permisos:** Visualización de sus cursos y certificados

**Credenciales (Opción 1 - Original):**

- **Usuario:** `12.345.678-5`
- **Contraseña:** `user123`

**Credenciales (Opción 2 - Nueva):**

- **Usuario:** `<EMAIL>`
- **Contraseña:** `participante123`

**Funcionalidades disponibles:**

- ✅ Mis Cursos
- ✅ Historial de Capacitaciones
- ✅ Descarga de Certificados
- ✅ Estado de Progreso

---

## 🚀 **INSTRUCCIONES DE ACCESO**

1. **Acceder al sistema:** Navegar a la URL del sistema
2. **Seleccionar credenciales:** Usar cualquiera de las credenciales listadas arriba
3. **Iniciar sesión:** El sistema redirigirá automáticamente al dashboard correspondiente
4. **Explorar funcionalidades:** Cada rol tiene acceso a diferentes secciones según sus permisos

---

## 📝 **NOTAS IMPORTANTES**

- ⚠️ **Estas credenciales son solo para demostración**
- 🔒 **En producción se implementarán credenciales seguras**
- 🎯 **Cada rol tiene funcionalidades específicas limitadas**
- 📊 **Los datos mostrados son simulados para la presentación**

---

## 🎨 **CARACTERÍSTICAS DEL SISTEMA**

- **Tema Claro/Oscuro:** Soporte completo para ambos modos
- **Responsive:** Funciona en desktop, tablet y móvil
- **Multirol:** Diferentes interfaces según el tipo de usuario
- **Tiempo Real:** Actualizaciones dinámicas de datos
- **Exportación:** Generación de reportes y certificados en PDF

---

## 📞 **SOPORTE TÉCNICO**

Para cualquier consulta durante la presentación:

- **Equipo de Desarrollo:** Disponible durante la demostración
- **Documentación:** Disponible en el repositorio del proyecto
- **Funcionalidades:** Todas las características están completamente implementadas

---

_Documento generado para la presentación al cliente - Sistema SUSTENTA_
