import React, { useState, useEffect } from 'react';
import { useCourseStore } from '../store/courseStore';
import { Course } from '../types';
import { AdminLayout } from '../components/layout/AdminLayout';
import { CourseTable } from '../components/courses/CourseTable';
import { CourseForm } from '../components/courses/CourseForm';
import { AttendanceTable } from '../components/attendance/AttendanceTable';
import { Modal } from '../components/ui/Modal';
import { Button } from '../components/ui/Button';
import { PlusIcon, DocumentArrowDownIcon, BookOpenIcon, AcademicCapIcon } from '@heroicons/react/24/outline';
import { generateBulkCertificates, downloadBlob, downloadPdfFromPublic } from '../lib/pdfGenerator';
import { DeleteConfirmationModal } from '../components/ui/ConfirmationModal';
import { HelpModal } from '../components/help/HelpModal';
import { useNotifications, NotificationTemplates } from '../contexts/ToastContext';
import {
  LazyReports,
  LazyAttendanceImport,
  LazyCertificateGenerator,
  LazyNotificationDemoComponent,
  DashboardSkeleton
} from '../components/lazy/LazyComponents';
import { SimpleCertificatePreview } from '../components/certificates/SimpleCertificatePreview';
import { PerformanceDemo } from '../components/demo/PerformanceDemo';
import { ValidationDemo } from '../components/demo/ValidationDemo';
import { SearchDemo } from '../components/demo/SearchDemo';
import { AccessibilityDemo } from '../components/demo/AccessibilityDemo';
import { ThemeDemo } from '../components/demo/ThemeDemo';
import { useThemeAware } from '../hooks/useTheme';
import { ErrorHandlingDemo } from '../components/demo/ErrorHandlingDemo';
import { ContractorManagement } from '../components/contractors/ContractorManagement';
import { SeatIconDemo } from '../components/demo/SeatIconDemo';
import { ImportAttendanceModal } from '../components/attendance/ImportAttendanceModal';
import { ImportGradesModal } from '../components/attendance/ImportGradesModal';

export function AdminDashboard() {
  // Component state
  const [activeSection, setActiveSection] = useState('dashboard');
  const [showCourseForm, setShowCourseForm] = useState(false);
  const [editingCourse, setEditingCourse] = useState<Course | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null);
  const [showAttendanceImport, setShowAttendanceImport] = useState(false);
  const [showGradesImport, setShowGradesImport] = useState(false);
  const [showSimpleAttendanceImport, setShowSimpleAttendanceImport] = useState(false);
  const [showSimpleGradesImport, setShowSimpleGradesImport] = useState(false);
  const [showBulkCertificates, setShowBulkCertificates] = useState<Course | null>(null);
  const [showCertificatePreview, setShowCertificatePreview] = useState(false);
  const [showHelp, setShowHelp] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);

  // Notifications
  const notifications = useNotifications();

  // Theme
  const theme = useThemeAware();

  const {
    courses,
    participants,
    loading,
    fetchCourses,
    fetchParticipants,
    addCourse,
    updateCourse,
    deleteCourse,
    updateAttendance
  } = useCourseStore();

  useEffect(() => {
    fetchCourses();
    fetchParticipants();
  }, [fetchCourses, fetchParticipants]);

  // Función para simular descarga de archivos
  const simulateFileDownload = (filename: string, mimeType: string) => {
    // Crear contenido simulado para el archivo
    const content = `Archivo simulado: ${filename}\nGenerado el: ${new Date().toLocaleString()}\nTipo: ${mimeType}`;
    const blob = new Blob([content], { type: mimeType });

    // Crear URL temporal y descargar
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // Función para descargar el certificado de Libro de Clases y Registro de Asistencias
  const handleDownloadLibroClases = async () => {
    try {
      notifications.info(
        'Descargando certificado',
        'Preparando descarga del Libro de Clases y Registro de Asistencias...',
        { duration: 2000 }
      );

      await downloadPdfFromPublic(
        'CERTIFICADO-LIBRO-CLASES-ASISTENCIA.pdf',
        'Libro_de_Clases_y_Registro_de_Asistencias.pdf'
      );

      notifications.success(
        'Descarga exitosa',
        'El certificado de Libro de Clases y Registro de Asistencias se ha descargado correctamente.',
        { duration: 3000 }
      );
    } catch (error) {
      console.error('Error downloading certificate:', error);
      notifications.error(
        'Error en la descarga',
        'No se pudo descargar el certificado. Por favor, inténtalo de nuevo.'
      );
    }
  };

  // Función para descargar el certificado de Asistencia y Aprobación
  const handleDownloadCertificadoInduccion = async () => {
    try {
      notifications.info(
        'Descargando certificado',
        'Preparando descarga del Certificado de Asistencia y Aprobación...',
        { duration: 2000 }
      );

      await downloadPdfFromPublic(
        'CERTIFICADO DE INDUCCION.pdf',
        'Certificado_de_Asistencia_y_Aprobacion.pdf'
      );

      notifications.success(
        'Descarga exitosa',
        'El certificado de Asistencia y Aprobación se ha descargado correctamente.',
        { duration: 3000 }
      );
    } catch (error) {
      console.error('Error downloading certificate:', error);
      notifications.error(
        'Error en la descarga',
        'No se pudo descargar el certificado. Por favor, inténtalo de nuevo.'
      );
    }
  };

  const handleCreateCourse = () => {
    setEditingCourse(null);
    setShowCourseForm(true);
  };

  const handleEditCourse = (course: Course) => {
    setEditingCourse(course);
    setShowCourseForm(true);
  };

  const handleDeleteCourse = (courseId: string) => {
    setShowDeleteConfirm(courseId);
  };

  const confirmDelete = async () => {
    if (!showDeleteConfirm) return;

    const course = courses.find(c => c.id === showDeleteConfirm);
    const courseName = course?.nombre || 'curso';

    setDeleteLoading(true);
    try {
      await deleteCourse(showDeleteConfirm);
      fetchCourses();
      setShowDeleteConfirm(null);

      // Notificación de éxito
      notifications.success(
        'Curso eliminado',
        `El curso "${courseName}" se ha eliminado correctamente.`
      );
    } catch (error) {
      console.error('Error deleting course:', error);

      // Notificación de error
      notifications.error(
        'Error al eliminar curso',
        `No se pudo eliminar el curso "${courseName}". Inténtalo nuevamente.`
      );
    } finally {
      setDeleteLoading(false);
    }
  };

  const handleCourseSubmit = async (data: any) => {
    try {
      if (editingCourse) {
        await updateCourse(editingCourse.id, data);
        notifications.success(
          'Curso actualizado',
          `El curso "${data.nombre}" se ha actualizado correctamente.`
        );
      } else {
        await addCourse(data);
        notifications.success(
          'Curso creado',
          `El curso "${data.nombre}" se ha creado correctamente.`
        );
      }
      setShowCourseForm(false);
      setEditingCourse(null);
      fetchCourses();
    } catch (error) {
      console.error('Error saving course:', error);
      notifications.error(
        editingCourse ? 'Error al actualizar curso' : 'Error al crear curso',
        'Ha ocurrido un error inesperado. Inténtalo nuevamente.'
      );
    }
  };

  const handleGenerateCertificates = async (courseId?: string) => {
    try {
      // Buscar curso - si no se especifica, usar el primer curso disponible
      const course = courseId ? courses.find(c => c.id === courseId) : courses[0];

      if (!course) {
        notifications.error(
          'Curso no encontrado',
          'No se pudo encontrar el curso seleccionado.'
        );
        return;
      }

      // Buscar participantes aprobados - usar lógica más flexible
      let approvedParticipants = participants.filter(p => p.estado === 'aprobado');

      // Si no hay participantes aprobados, crear algunos de ejemplo para la simulación
      if (approvedParticipants.length === 0) {
        // Crear participantes de ejemplo para la demostración
        approvedParticipants = [
          {
            id: 'demo-1',
            nombre: 'Juan Carlos Pérez',
            rut: '12.345.678-9',
            contractor: 'Empresa Demo S.A.',
            sessionId: course.id,
            estado: 'aprobado' as const,
            asistencia: 100,
            nota: 85
          },
          {
            id: 'demo-2',
            nombre: 'María González Silva',
            rut: '98.765.432-1',
            contractor: 'Constructora ABC Ltda.',
            sessionId: course.id,
            estado: 'aprobado' as const,
            asistencia: 95,
            nota: 92
          },
          {
            id: 'demo-3',
            nombre: 'Roberto Fernández López',
            rut: '11.222.333-4',
            contractor: 'Tech Solutions SpA',
            sessionId: course.id,
            estado: 'aprobado' as const,
            asistencia: 100,
            nota: 88
          }
        ];

        notifications.info(
          'Usando datos de demostración',
          'Se generarán certificados con participantes de ejemplo para la demostración.',
          { duration: 3000 }
        );
      }

      // Mostrar notificación de inicio
      notifications.info(
        'Generando certificados',
        `Iniciando generación de ${approvedParticipants.length} certificados para "${course.nombre}"...`,
        { duration: 2000 }
      );

      // Simular progreso de generación
      setTimeout(() => {
        notifications.info(
          'Procesando certificados',
          'Creando archivos PDF y preparando descarga...',
          { duration: 2000 }
        );
      }, 1000);

      // Simular descarga después del procesamiento
      setTimeout(() => {
        // Simular descarga de archivo ZIP
        simulateFileDownload(
          `certificados_${course.codigo}_${new Date().toISOString().split('T')[0]}.zip`,
          'application/zip'
        );

        notifications.success(
          'Certificados descargados exitosamente',
          `Se han generado y descargado ${approvedParticipants.length} certificados para "${course.nombre}". Revisa tu carpeta de descargas.`,
          { duration: 5000 }
        );
      }, 3500);

    } catch (error) {
      console.error('Error generating certificates:', error);
      notifications.error(
        'Error al generar certificados',
        'Ocurrió un error durante la generación de certificados. Por favor, inténtalo de nuevo.'
      );
    }
  };

  const handleImportAttendance = (attendanceData: any[]) => {
    try {
      let successCount = 0;
      let errorCount = 0;

      // Simulate importing attendance data
      attendanceData.forEach(data => {
        const participant = participants.find(p => p.rut === data.rut);
        if (participant) {
          updateAttendance(participant.id, data.asistencia, participant.calificacion);
          successCount++;
        } else {
          errorCount++;
        }
      });

      setShowAttendanceImport(false);

      // Notificaciones basadas en el resultado
      if (successCount > 0 && errorCount === 0) {
        notifications.success(
          'Asistencia importada',
          `Se importaron ${successCount} registros de asistencia correctamente.`
        );
      } else if (successCount > 0 && errorCount > 0) {
        notifications.warning(
          'Importación parcial',
          `Se importaron ${successCount} registros. ${errorCount} registros no pudieron procesarse.`
        );
      } else {
        notifications.error(
          'Error en importación',
          'No se pudo importar ningún registro de asistencia.'
        );
      }
    } catch (error) {
      console.error('Error importing attendance:', error);
      notifications.error(
        'Error en importación',
        'Ha ocurrido un error al importar la asistencia.'
      );
    }
  };

  const handleImportGrades = (gradesData: any[]) => {
    try {
      let successCount = 0;
      let errorCount = 0;

      // Simulate importing grades data
      gradesData.forEach(data => {
        const participant = participants.find(p => p.rut === data.rut);
        if (participant) {
          updateAttendance(participant.id, participant.asistencia, data.calificacion);
          successCount++;
        } else {
          errorCount++;
        }
      });

      setShowGradesImport(false);

      // Notificaciones basadas en el resultado
      if (successCount > 0 && errorCount === 0) {
        notifications.success(
          'Calificaciones importadas',
          `Se importaron ${successCount} registros de calificaciones correctamente.`
        );
      } else if (successCount > 0 && errorCount > 0) {
        notifications.warning(
          'Importación parcial',
          `Se importaron ${successCount} registros. ${errorCount} registros no pudieron procesarse.`
        );
      } else {
        notifications.error(
          'Error en importación',
          'No se pudo importar ningún registro de calificaciones.'
        );
      }
    } catch (error) {
      console.error('Error importing grades:', error);
      notifications.error(
        'Error en importación',
        'Ha ocurrido un error al importar las calificaciones.'
      );
    }
  };

  const handleExportReport = () => {
    try {
      // Simulate PDF export
      notifications.info(
        'Generando reporte',
        'El reporte se está generando. Te notificaremos cuando esté listo.',
        { duration: 3000 }
      );

      // Simular proceso de generación
      setTimeout(() => {
        notifications.success(
          'Reporte generado',
          'El reporte se ha generado correctamente y se ha descargado.',
          {
            action: {
              label: 'Ver carpeta',
              onClick: () => notifications.info('Abriendo carpeta de descargas...')
            }
          }
        );
      }, 2000);
    } catch (error) {
      notifications.error(
        'Error al generar reporte',
        'No se pudo generar el reporte. Inténtalo nuevamente.'
      );
    }
  };

  const renderContent = () => {
    switch (activeSection) {
      case 'dashboard':
        return (
          <div className="space-y-8">
            {/* Header con tipografía consistente */}
            <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm overflow-hidden">
              <div className="p-8">
                <h1 className="section-title text-3xl mb-2">
                  BIENVENIDO AL PANEL DE ADMINISTRACIÓN
                </h1>
                <p className="font-sans text-base text-gray-600 dark:text-gray-400">
                  GESTIONA CURSOS, PARTICIPANTES Y CERTIFICADOS DESDE ESTE PANEL CENTRALIZADO
                </p>
              </div>
            </div>

            {/* Métricas principales con temas adaptativos */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Total Cursos - Color azul */}
              <div className={`${theme.bg} border ${theme.border} rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow`}>
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className={`font-sans text-sm font-medium ${theme.textSecondary} uppercase tracking-wide`}>
                      Total Cursos
                    </h3>
                    <p className="font-sans text-3xl font-bold text-blue-600 dark:text-blue-400 mt-2">
                      {courses.length}
                    </p>
                  </div>
                  <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
                    <div className="w-6 h-6 bg-blue-500 dark:bg-blue-400 rounded"></div>
                  </div>
                </div>
              </div>

              {/* Participantes - Color verde */}
              <div className={`${theme.bg} border ${theme.border} rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow`}>
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className={`font-sans text-sm font-medium ${theme.textSecondary} uppercase tracking-wide`}>
                      Participantes
                    </h3>
                    <p className="font-sans text-3xl font-bold text-green-600 dark:text-green-400 mt-2">
                      {participants.length}
                    </p>
                  </div>
                  <div className="bg-green-50 dark:bg-green-900/20 p-3 rounded-lg">
                    <div className="w-6 h-6 bg-green-500 dark:bg-green-400 rounded"></div>
                  </div>
                </div>
              </div>

              {/* Aprobados - Color naranja */}
              <div className={`${theme.bg} border ${theme.border} rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow`}>
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className={`font-sans text-sm font-medium ${theme.textSecondary} uppercase tracking-wide`}>
                      Aprobados
                    </h3>
                    <p className="font-sans text-3xl font-bold text-orange-600 dark:text-orange-400 mt-2">
                      {participants.filter(p => p.estado === 'aprobado').length}
                    </p>
                    <p className={`font-sans text-sm ${theme.textMuted} mt-1`}>
                      {participants.length > 0
                        ? `${Math.round((participants.filter(p => p.estado === 'aprobado').length / participants.length) * 100)}% del total`
                        : 'Sin datos'
                      }
                    </p>
                  </div>
                  <div className="bg-orange-50 dark:bg-orange-900/20 p-3 rounded-lg">
                    <div className="w-6 h-6 bg-orange-500 dark:bg-orange-400 rounded"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      case 'courses':
        return (
          <div className="w-full space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="section-title">GESTIÓN DE CURSOS</h2>
              <Button onClick={handleCreateCourse} className="flex items-center space-x-2">
                <PlusIcon className="w-4 h-4" />
                <span>Nuevo Curso</span>
              </Button>
            </div>

            <div className="w-full">
              <CourseTable
                courses={courses}
                onEdit={handleEditCourse}
                onDelete={handleDeleteCourse}
              />
            </div>
          </div>
        );
        
      case 'attendance':
        return (
          <AttendanceTable
            participants={participants}
            onUpdateAttendance={updateAttendance}
            onImportAttendance={() => setShowSimpleAttendanceImport(true)}
            onImportGrades={() => setShowSimpleGradesImport(true)}
            onExportReport={handleExportReport}
          />
        );
        
      case 'certificates':
        return (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="section-title">GESTIÓN DE CERTIFICADOS</h2>
              <div className="space-x-3">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowHelp(true)}
                  className="text-white hover:text-gray-200 dark:text-white dark:hover:text-gray-300"
                >
                  Ayuda
                </Button>
              </div>
            </div>

            {/* Sección 1: Libro de Clases y Registro de Asistencias */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center mb-4">
                <BookOpenIcon className="w-6 h-6 text-blue-600 mr-3" />
                <h3 className="font-sans text-xl font-semibold text-gray-900 dark:text-gray-100">
                  Libro de Clases y Registro de Asistencias
                </h3>
              </div>

              <p className="font-sans text-gray-600 dark:text-gray-400 mb-6">
                Descarga el certificado oficial del Libro de Clases y Registro de Asistencias para documentación administrativa.
              </p>

              <div className="flex items-center justify-between bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                <div className="flex items-center">
                  <div className="bg-blue-100 dark:bg-blue-800 p-2 rounded-lg mr-4">
                    <DocumentArrowDownIcon className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <h4 className="font-sans font-medium text-gray-900 dark:text-gray-100">
                      Certificado de Libro de Clases
                    </h4>
                    <p className="font-sans text-sm text-gray-600 dark:text-gray-400">
                      Documento oficial para registro administrativo
                    </p>
                  </div>
                </div>
                <Button
                  onClick={handleDownloadLibroClases}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  <DocumentArrowDownIcon className="w-4 h-4 mr-2" />
                  Descargar PDF
                </Button>
              </div>
            </div>

            {/* Sección 2: Certificado de Asistencia y Aprobación */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center mb-4">
                <AcademicCapIcon className="w-6 h-6 text-green-600 mr-3" />
                <h3 className="font-sans text-xl font-semibold text-gray-900 dark:text-gray-100">
                  Certificado de Asistencia y Aprobación
                </h3>
              </div>

              <p className="font-sans text-gray-600 dark:text-gray-400 mb-6">
                Descarga el certificado oficial de Asistencia y Aprobación para participantes que completaron exitosamente el curso.
              </p>

              <div className="flex items-center justify-between bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                <div className="flex items-center">
                  <div className="bg-green-100 dark:bg-green-800 p-2 rounded-lg mr-4">
                    <AcademicCapIcon className="w-6 h-6 text-green-600 dark:text-green-400" />
                  </div>
                  <div>
                    <h4 className="font-sans font-medium text-gray-900 dark:text-gray-100">
                      Certificado de Inducción
                    </h4>
                    <p className="font-sans text-sm text-gray-600 dark:text-gray-400">
                      Documento de aprobación y asistencia
                    </p>
                  </div>
                </div>
                <Button
                  onClick={handleDownloadCertificadoInduccion}
                  className="bg-green-600 hover:bg-green-700 text-white"
                >
                  <DocumentArrowDownIcon className="w-4 h-4 mr-2" />
                  Descargar PDF
                </Button>
              </div>
            </div>

            {/* Sección de Certificados Personalizados (funcionalidad original) */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center mb-4">
                <DocumentArrowDownIcon className="w-6 h-6 text-purple-600 mr-3" />
                <h3 className="font-sans text-xl font-semibold text-gray-900 dark:text-gray-100">
                  Certificados Personalizados
                </h3>
              </div>

              <p className="font-sans text-gray-600 dark:text-gray-400 mb-4">
                Genere certificados personalizados para participantes aprobados de cualquier curso.
              </p>

              {participants.filter(p => p.estado === 'aprobado').length === 0 ? (
                <div className="text-center py-8">
                  <p className="font-sans text-gray-500 dark:text-gray-400">No hay participantes aprobados para generar certificados</p>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
                    <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                      {participants.filter(p => p.estado === 'aprobado').length}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">Certificados Disponibles</div>
                  </div>
                </div>
              )}
            </div>
          </div>
        );

      case 'contractors':
        return <ContractorManagement />;

      case 'reports':
        return (
          <LazyReports participants={participants} courses={courses} />
        );

      case 'notifications':
        return (
          <div className="space-y-6">
            <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg border border-gray-200 dark:border-gray-700">
              <div className="p-6">
                <h2 className="section-title mb-4">Sistema de Notificaciones</h2>
                <LazyNotificationDemoComponent />
              </div>
            </div>
          </div>
        );

      case 'performance':
        return (
          <div className="space-y-6">
            <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg border border-gray-200 dark:border-gray-700">
              <div className="p-6">
                <h2 className="section-title mb-4">Optimizaciones de Rendimiento</h2>
                <PerformanceDemo />
              </div>
            </div>
          </div>
        );

      case 'validation':
        return (
          <div className="space-y-6">
            <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg border border-gray-200 dark:border-gray-700">
              <div className="p-6">
                <h2 className="section-title mb-4">Validación de Formularios</h2>
                <ValidationDemo />
              </div>
            </div>
          </div>
        );

      case 'search':
        return (
          <div className="space-y-6">
            <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg border border-gray-200 dark:border-gray-700">
              <div className="p-6">
                <h2 className="section-title mb-4">Búsqueda y Filtros Avanzados</h2>
                <SearchDemo />
              </div>
            </div>
          </div>
        );

      case 'accessibility':
        return (
          <div className="space-y-6">
            <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg border border-gray-200 dark:border-gray-700">
              <div className="p-6">
                <h2 className="section-title mb-4">Accesibilidad (a11y)</h2>
                <AccessibilityDemo />
              </div>
            </div>
          </div>
        );

      case 'theme':
        return (
          <div className="space-y-6">
            <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg border border-gray-200 dark:border-gray-700">
              <div className="p-6">
                <h2 className="section-title mb-4">Sistema de Temas</h2>
                <ThemeDemo />
              </div>
            </div>
          </div>
        );

      case 'errors':
        return (
          <div className="space-y-6">
            <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg border border-gray-200 dark:border-gray-700">
              <div className="p-6">
                <h2 className="section-title mb-4">Manejo de Errores</h2>
                <ErrorHandlingDemo />
              </div>
            </div>
          </div>
        );

      case 'seat-icons':
        return (
          <div className="space-y-6">
            <SeatIconDemo />
          </div>
        );

      default:
        return null;
    }
  };

  const getBreadcrumbs = () => {
    switch (activeSection) {
      case 'dashboard':
        return [];
      case 'courses':
        return [
          { label: 'GESTIÓN DE CURSOS', current: true }
        ];
      case 'attendance':
        return [
          { label: 'ASISTENCIA Y CALIFICACIONES', current: true }
        ];
      case 'certificates':
        return [
          { label: 'CERTIFICADOS', current: true }
        ];
      case 'contractors':
        return [
          { label: 'GESTIÓN DE EMPRESAS', current: true }
        ];
      case 'reports':
        return [
          { label: 'REPORTES', current: true }
        ];
      case 'notifications':
        return [
          { label: 'NOTIFICACIONES', current: true }
        ];
      case 'performance':
        return [
          { label: 'RENDIMIENTO', current: true }
        ];
      case 'validation':
        return [
          { label: 'VALIDACIÓN', current: true }
        ];
      case 'search':
        return [
          { label: 'BÚSQUEDA', current: true }
        ];
      case 'accessibility':
        return [
          { label: 'ACCESIBILIDAD', current: true }
        ];
      case 'theme':
        return [
          { label: 'TEMAS', current: true }
        ];
      case 'errors':
        return [
          { label: 'ERRORES', current: true }
        ];
      case 'seat-icons':
        return [
          { label: 'ICONOS DE BUTACAS', current: true }
        ];
      default:
        return [];
    }
  };

  const getSectionTitle = () => {
    switch (activeSection) {
      case 'dashboard':
        return 'PANEL DE ADMINISTRACIÓN';
      case 'courses':
        return 'GESTIÓN DE CURSOS';
      case 'attendance':
        return 'ASISTENCIA Y CALIFICACIONES';
      case 'certificates':
        return 'GESTIÓN DE CERTIFICADOS';
      case 'contractors':
        return 'GESTIÓN DE EMPRESAS';
      case 'reports':
        return 'REPORTES Y ANÁLISIS';
      case 'notifications':
        return 'SISTEMA DE NOTIFICACIONES';
      case 'performance':
        return 'OPTIMIZACIONES DE RENDIMIENTO';
      case 'validation':
        return 'VALIDACIÓN DE FORMULARIOS';
      case 'search':
        return 'BÚSQUEDA Y FILTROS AVANZADOS';
      case 'accessibility':
        return 'ACCESIBILIDAD (A11Y)';
      case 'theme':
        return 'SISTEMA DE TEMAS';
      case 'errors':
        return 'MANEJO DE ERRORES';
      case 'seat-icons':
        return 'ICONOS DE BUTACAS SVG';
      default:
        return 'PANEL DE ADMINISTRACIÓN';
    }
  };

  return (
    <AdminLayout
      activeSection={activeSection}
      onSectionChange={setActiveSection}
      breadcrumbs={getBreadcrumbs()}
      title={getSectionTitle()}
      onHelpClick={() => setShowHelp(true)}
    >
      {renderContent()}
      
      {/* Course Form Modal */}
      <Modal
        isOpen={showCourseForm}
        onClose={() => {
          setShowCourseForm(false);
          setEditingCourse(null);
        }}
        title={editingCourse ? 'Editar Curso' : 'Crear Nuevo Curso'}
        size="lg"
      >
        <CourseForm
          course={editingCourse || undefined}
          onSubmit={handleCourseSubmit}
          onCancel={() => {
            setShowCourseForm(false);
            setEditingCourse(null);
          }}
          loading={loading}
        />
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={!!showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(null)}
        title="Confirmar Eliminación"
      >
        <div className="space-y-4">
          <p className="text-gray-700">
            ¿Está seguro de que desea eliminar este curso? Esta acción no se puede deshacer.
          </p>
          
          <div className="flex justify-end space-x-3">
            <Button
              variant="secondary"
              onClick={() => setShowDeleteConfirm(null)}
            >
              Cancelar
            </Button>
            <Button
              variant="danger"
              onClick={confirmDelete}
            >
              Eliminar Curso
            </Button>
          </div>
        </div>
      </Modal>

      {/* Attendance Import Modal */}
      {showAttendanceImport && (
        <LazyAttendanceImport
          isOpen={showAttendanceImport}
          onClose={() => setShowAttendanceImport(false)}
          onImport={handleImportAttendance}
          existingParticipants={participants.map(p => ({ rut: p.rut, nombre: p.nombre }))}
        />
      )}

      {/* Grades Import Modal */}
      {showGradesImport && (
        <LazyAttendanceImport
          isOpen={showGradesImport}
          onClose={() => setShowGradesImport(false)}
          onImport={handleImportGrades}
          existingParticipants={participants.map(p => ({ rut: p.rut, nombre: p.nombre }))}
        />
      )}

      {/* Bulk Certificate Generator */}
      {showBulkCertificates && (
        <LazyCertificateGenerator
          isOpen={true}
          onClose={() => setShowBulkCertificates(null)}
          participants={participants.filter(p =>
            // In a real app, this would be filtered by session
            p.sessionId === showBulkCertificates.id
          )}
          course={showBulkCertificates}
        />
      )}

      {/* Certificate Preview - Oculto por solicitud del cliente */}
      {/* {showCertificatePreview && (
        <SimpleCertificatePreview
          isOpen={showCertificatePreview}
          onClose={() => setShowCertificatePreview(false)}
        />
      )} */}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <DeleteConfirmationModal
          isOpen={true}
          onClose={() => setShowDeleteConfirm(null)}
          onConfirm={confirmDelete}
          itemName={courses.find(c => c.id === showDeleteConfirm)?.nombre || 'Curso'}
          itemType="curso"
          loading={deleteLoading}
        />
      )}

      {/* Simple Import Modals */}
      <ImportAttendanceModal
        isOpen={showSimpleAttendanceImport}
        onClose={() => setShowSimpleAttendanceImport(false)}
      />

      <ImportGradesModal
        isOpen={showSimpleGradesImport}
        onClose={() => setShowSimpleGradesImport(false)}
      />

      {/* Help Modal */}
      <HelpModal
        isOpen={showHelp}
        onClose={() => setShowHelp(false)}
        userRole="administrador"
      />
    </AdminLayout>
  );
}