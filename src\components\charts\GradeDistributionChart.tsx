import React, { useMemo } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveContainer, <PERSON>, Toolt<PERSON> } from 'recharts';

interface GradeDistributionChartProps {
  data: Array<{
    range: string;
    count: number;
    percentage: number;
  }>;
}

const COLORS = {
  'Excelente (6.0-7.0)': '#10B981', // green-500
  'Bueno (5.0-5.9)': '#3B82F6',     // blue-500
  'Regular (4.0-4.9)': '#F59E0B',   // amber-500
  'Insuficiente (1.0-3.9)': '#EF4444' // red-500
};

export function GradeDistributionChart({ data }: GradeDistributionChartProps) {
  const memoizedData = useMemo(() => data, [data]);

  const renderCustomizedLabel = useMemo(() => ({ cx, cy, midAngle, innerRadius, outerRadius, percentage }: any) => {
    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text 
        x={x} 
        y={y} 
        fill="white" 
        textAnchor={x > cx ? 'start' : 'end'} 
        dominantBaseline="central"
        fontSize="12"
        fontWeight="bold"
      >
        {`${percentage.toFixed(1)}%`}
      </text>
    );
  }, []);

  const CustomTooltip = useMemo(() => ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900">{data.range}</p>
          <p className="text-sm text-gray-600">
            <span className="font-medium">{data.count}</span> estudiantes ({data.percentage.toFixed(1)}%)
          </p>
        </div>
      );
    }
    return null;
  }, []);

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        Distribución de Notas
      </h3>
      <div className="h-80">
        <ResponsiveContainer width="100%" height="100%">
          <PieChart>
            <Pie
              data={memoizedData}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={renderCustomizedLabel}
              outerRadius={100}
              fill="#8884d8"
              dataKey="count"
              animationBegin={0}
              animationDuration={0}
              isAnimationActive={false}
            >
              {memoizedData.map((entry, index) => (
                <Cell 
                  key={`cell-${index}`} 
                  fill={COLORS[entry.range as keyof typeof COLORS]} 
                />
              ))}
            </Pie>
            <Tooltip content={<CustomTooltip />} />
            <Legend 
              verticalAlign="bottom" 
              height={36}
              formatter={(value, entry) => (
                <span style={{ color: entry.color }}>
                  {value}
                </span>
              )}
            />
          </PieChart>
        </ResponsiveContainer>
      </div>
      <div className="mt-4 grid grid-cols-2 gap-4 text-sm">
        {memoizedData.map((item, index) => (
          <div key={index} className="flex items-center">
            <div 
              className="w-3 h-3 rounded-full mr-2"
              style={{ backgroundColor: COLORS[item.range as keyof typeof COLORS] }}
            ></div>
            <span className="text-gray-600">
              {item.range}: <span className="font-medium">{item.count}</span>
            </span>
          </div>
        ))}
      </div>
    </div>
  );
}
